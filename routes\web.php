<?php

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Middleware\Cors;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Middleware\VerifyCsrfToken;


//Auth::routes();

Route::post('/login', [AuthController::class, 'login'])->withoutMiddleware([VerifyCsrfToken::class])->name('login');
Route::get('/logout', 'App\Http\Controllers\Auth\LoginController@logout');
//Route::get('/logout', 'App\Http\Controllers\Auth\LoginController@logout')->name('logout');

// Ruta para obtener CSRF cookie (alternativa a la de Sanctum)
Route::get('/sanctum/csrf-cookie', function () {
    return response()->json([
        'csrf_token' => csrf_token()
    ]);
})
->middleware(\App\Http\Middleware\SanctumCors::class)
//->withoutMiddleware([VerifyCsrfToken::class])
;

Route::post('/sanctum/get-token', function (Request $request) {

    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    $user = User::where('email', $request->email)->first();
    if ( ! $user || ! Hash::check($request->password, $user->password)) {
        return [ 'wrongLogin' => ['The provided credentials are incorrect.'] ];
    }
    //Log::info($user);
    $user->tokens()->delete();
    return [ 'token' => [ $user->createToken('user-token', ['*'], now()->addMinutes( (int) env('SANCTUM_TOKEN_EXPIRATION', 1400)))->plainTextToken]
];
})
  ->middleware(\App\Http\Middleware\SanctumCors::class)
  ;

Route::post('/sanctum/verify-token', [AuthController::class, 'verifyToken'])->name('verifyToken')
    //->middleware(['throttle:60,1'])
    ->middleware(\App\Http\Middleware\SanctumCors::class)
    ;

//Route::group(['middleware' => 'auth'], function () {
Route::prefix('api')->middleware('auth:sanctum')->withoutMiddleware([VerifyCsrfToken::class])->group(function () {

    /*
    Route::get('/', 'App\Http\Controllers\HomeController@index')->name('home');
    Route::get('/home', 'App\Http\Controllers\HomeController@index')->name('home.home');

    Route::get('/trasteros', 'App\Http\Controllers\TrasteroController@listado')->name('trasteros.listado');
    Route::get('/trasteros/nuevo', 'App\Http\Controllers\TrasteroController@nuevo')->name('trasteros.nuevo');
    Route::post('/trasteros/guardar-nuevo', 'App\Http\Controllers\TrasteroController@guardar')->name('trasteros.guardar');
    Route::get('/trasteros/editar/{trastero}', 'App\Http\Controllers\TrasteroController@editar')->name('trasteros.editar');
    Route::post('/trasteros/guardar-editar/{trastero}', 'App\Http\Controllers\TrasteroController@guardarEditar')->name('trasteros.guardarEditar');
    Route::get('/trasteros/ver/{trastero}', 'App\Http\Controllers\TrasteroController@ver')->name('trasteros.ver');
    Route::get('/trasteros/informes', 'App\Http\Controllers\TrasteroController@informes')->name('trasteros.informes');
    Route::get('/trasteros/informes/{trastero}', 'App\Http\Controllers\TrasteroController@informesTrastero')->name('trasteros.informesTrastero');

    Route::get('/clientes/posible/{posible}/', 'App\Http\Controllers\ClienteController@listado')->name('clientes.listado');
    Route::get('/clientes/nuevo/posible/{posible}/', 'App\Http\Controllers\ClienteController@nuevo')->name('clientes.nuevo');
    Route::post('/clientes/guardar-nuevo/posible/{posible}/', 'App\Http\Controllers\ClienteController@guardar')->name('clientes.guardar');
    Route::get('/clientes/editar/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@editar')->name('clientes.editar');
    Route::post('/clientes/guardar-editar/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@guardarEditar')->name('clientes.guardarEditar');
    Route::get('/clientes/ver/{cliente}', 'App\Http\Controllers\ClienteController@ver')->name('clientes.ver');
    Route::get('/clientes/informes/posible/{posible}/', 'App\Http\Controllers\ClienteController@informes')->name('clientes.informes');
    Route::get('/clientes/informes/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@informesTrastero')->name('clientes.informesTrastero');

    Route::post('/clientes/borrar/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@borrar')->name('clientes.borrar');
    Route::post('/clientes/hacer-cliente/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@hacerCliente')->name('clientes.hacer.cliente');

    Route::get('/posibles-clientes/posible/{posible}/', 'App\Http\Controllers\ClienteController@listado')->name('posibles.clientes.listado');
    Route::get('/posibles-clientes/nuevo/posible/{posible}/', 'App\Http\Controllers\ClienteController@nuevo')->name('posibles.clientes.nuevo');
    Route::post('/posibles-clientes/guardar-nuevo/posible/{posible}/', 'App\Http\Controllers\ClienteController@guardar')->name('posibles.clientes.guardar');
    Route::get('/posibles-clientes/editar/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@editar')->name('posibles.clientes.editar');
    Route::post('/posibles-clientes/guardar-editar/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@guardarEditar')->name('posibles.clientes.guardarEditar');
    Route::get('/posibles-clientes/ver/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@ver')->name('posibles.clientes.ver');
    Route::get('/posibles-clientes/informes/posible/{posible}/', 'App\Http\Controllers\ClienteController@informes')->name('posibles.clientes.informes');
    Route::get('/posibles-clientes/informes/{cliente}/posible/{posible}/', 'App\Http\Controllers\ClienteController@informesTrastero')->name('posibles.clientes.informesTrastero');

    Route::get('/pagos', 'App\Http\Controllers\PagoController@listado')->name('pagos.listado');
    Route::get('/pagos/nuevo', 'App\Http\Controllers\PagoController@nuevo')->name('pagos.nuevo');
    Route::post('/pagos/guardar-nuevo', 'App\Http\Controllers\PagoController@guardar')->name('pagos.guardar');
    Route::get('/pagos/editar/{pago}', 'App\Http\Controllers\PagoController@editar')->name('pagos.editar');
    Route::post('/pagos/guardar-editar/{pago}', 'App\Http\Controllers\PagoController@guardarEditar')->name('pagos.guardarEditar');
    Route::get('/pagos/ver/{pago}', 'App\Http\Controllers\PagoController@ver')->name('pagos.ver');
    Route::get('/pagos/informes', 'App\Http\Controllers\PagoController@informes')->name('pagos.informes');
    Route::get('/pagos/informes/{pago}', 'App\Http\Controllers\PagoController@informesTrastero')->name('pagos.informesTrastero');

    Route::any('/pagos/recibo/{pago}', 'App\Http\Controllers\PDFController@reciboPago')->name('pagos.recibo');

    Route::get('/periodos', 'App\Http\Controllers\PeriodoController@listado')->name('periodos.listado');
    Route::get('/periodos/nuevo', 'App\Http\Controllers\PeriodoController@nuevo')->name('periodos.nuevo');
    Route::post('/periodos/guardar-nuevo', 'App\Http\Controllers\PeriodoController@guardar')->name('periodos.guardar');
    Route::get('/periodos/editar/{periodo}', 'App\Http\Controllers\PeriodoController@editar')->name('periodos.editar');
    Route::post('/periodos/guardar-editar/{periodo}', 'App\Http\Controllers\PeriodoController@guardarEditar')->name('periodos.guardarEditar');
    Route::get('/periodos/ver/{periodo}', 'App\Http\Controllers\PeriodoController@ver')->name('periodos.ver');
    Route::get('/periodos/informes', 'App\Http\Controllers\PeriodoController@informes')->name('periodos.informes');
    Route::get('/periodos/informes/{periodo}', 'App\Http\Controllers\PeriodoController@informesTrastero')->name('periodos.informesTrastero');

    Route::get('/precios', 'App\Http\Controllers\PrecioController@listado')->name('precios.listado');
    Route::get('/precios/nuevo', 'App\Http\Controllers\PrecioController@nuevo')->name('precios.nuevo');
    Route::post('/precios/guardar-nuevo', 'App\Http\Controllers\PrecioController@guardar')->name('precios.guardar');
    Route::get('/precios/editar/{trastero}', 'App\Http\Controllers\PrecioController@editar')->name('precios.editar');
    Route::post('/precios/guardar-editar/{trastero}', 'App\Http\Controllers\PrecioController@guardarEditar')->name('precios.guardarEditar');
    Route::get('/precios/ver/{trastero}', 'App\Http\Controllers\PrecioController@ver')->name('precios.ver');
    Route::get('/precios/informes', 'App\Http\Controllers\PrecioController@informes')->name('precios.informes');
    Route::get('/precios/informes/{trastero}', 'App\Http\Controllers\PrecioController@informesTrastero')->name('precios.informesTrastero');

    Route::any('/periodo/recibo/{periodo}', 'App\Http\Controllers\PDFController@reciboPeriodo')->name('periodos.recibo');

    Route::get('/cron-set-alquileres', 'App\Http\Controllers\CronController@setAlquileres')->name('cron.set.alquileres');

    Route::get('/create-pdf-recibo', 'App\Http\Controllers\PDFController@recibo')->name('pdf.recibo');

    Route::post('/ajax/get-alquileres-cliente/', 'App\Http\Controllers\AjaxController@getAlquileresCliente')->name('ajax.get.alquileres.cliente');
    */
});


